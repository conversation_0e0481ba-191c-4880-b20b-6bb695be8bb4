export const discountTrafficModifier = 'discount-traffic';

export const discountTrafficFormModifier = `${discountTrafficModifier}__form`;

export const discountTrafficAutocompleteModifier = `${discountTrafficModifier}__autocomplete`;

export const discountTrafficCalendarModifier = `${discountTrafficModifier}__calendar`;

export const discountTrafficInputModifier = `${discountTrafficModifier}__input`;

export const discountTrafficRadioButtonsModifier = `${discountTrafficModifier}__radio-buttons`;

export const discountFields = {
  homeOperators: 'home_operators',
  partnerOperators: 'partner_operators',
  validFrom: 'start_date',
  validTo: 'end_date',
  discountDirection: 'direction',
  serviceTypes: 'service_types',
  discountSettlementMethod: 'settlement_method',
  discountCurrency: 'currency_code',
  taxType: 'tax_type',
  volumeType: 'volume_type',
  callDestinations: 'call_destinations',
  calledCountries: 'called_countries',
  trafficSegments: 'traffic_segments',
  imsiCountType: 'imsi_count_type',
  rateAboveCommitment: 'above_commitment_rate',
  inboundMarketShare: 'inbound_market_share',
  subDiscounts: 'sub_discounts',
  financialThreshold: 'financial_threshold',
  aboveThresholdRate: 'above_financial_threshold_rate',
};
