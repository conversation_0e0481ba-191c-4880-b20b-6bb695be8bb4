import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import getIds from 'core/utilities/getIds';
import getArrayByKey from 'core/utilities/getArrayByKey';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import {
  discountQualifyingFields,
  discountQualifyingRule,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountQualifying/constants';
import {
  isDiscountQualifyingRequiredFieldsEmpty,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountQualifying/utilities';
import {
  discountModelTypeField,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountModelType/constants';
import {
  commitmentDistributionParameters,
  commitmentDistributionParametersFields,
}
  from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/CommitmentDistributionParameters/constants';

const getConvertedFormattedNumber = (value) => (value === '' || value === null ? value : Number(value));

export const getConvertedForRequestFormData = (formData) => {
  const data = {
    [discountModelTypeField]: formData[discountModelTypeField].value,
    [discountFields.homeOperators]: getIds(formData[discountFields.homeOperators]),
    [discountFields.partnerOperators]: getIds(formData[discountFields.partnerOperators]),
    [discountFields.validTo]: formData[discountFields.validTo],
    [discountFields.validFrom]: formData[discountFields.validFrom],
    [discountFields.discountDirection]: formData[discountFields.discountDirection].value,
    [discountFields.serviceTypes]: getArrayByKey(formData[discountFields.serviceTypes], 'value'),
    [discountFields.callDestinations]: getArrayByKey(formData[discountFields.callDestinations], 'value'),
    [discountFields.calledCountries]: formData[discountFields.calledCountries],
    [discountFields.imsiCountType]: formData[discountFields.imsiCountType]?.value || null,
    [discountFields.trafficSegments]: getArrayByKey(formData[discountFields.trafficSegments], 'id'),
    [discountFields.discountSettlementMethod]: formData[discountFields.discountSettlementMethod]
      .value,
    [discountFields.discountCurrency]: formData[discountFields.discountCurrency].code,
    [discountFields.rateAboveCommitment]:
       getConvertedFormattedNumber(formData[discountFields.rateAboveCommitment]),
    [discountFields.inboundMarketShare]:
        getConvertedFormattedNumber(formData[discountFields.inboundMarketShare]),
    [discountFields.financialThreshold]:
        getConvertedFormattedNumber(formData[discountFields.financialThreshold]),
    [discountFields.aboveThresholdRate]:
        getConvertedFormattedNumber(formData[discountFields.aboveThresholdRate]),
    [discountFields.taxType]: formData[discountFields.taxType],
    [discountFields.volumeType]: formData[discountFields.volumeType],
    [discountQualifyingRule]:
        isDiscountQualifyingRequiredFieldsEmpty(formData[discountQualifyingRule])
          ? null
          : {
            [discountQualifyingFields.direction]:
        formData[discountQualifyingRule][discountQualifyingFields.direction]?.value,
            [discountQualifyingFields.serviceTypes]:
          getArrayByKey(formData[discountQualifyingRule][discountQualifyingFields.serviceTypes], 'value'),
            [discountQualifyingFields.basis]:
      formData[discountQualifyingRule][discountQualifyingFields.basis]?.value,
            [discountQualifyingFields.upperBound]: getConvertedFormattedNumber(
              formData[discountQualifyingRule][discountQualifyingFields.upperBound],
            ),
            [discountQualifyingFields.lowerBound]: getConvertedFormattedNumber(
              formData[discountQualifyingRule][discountQualifyingFields.lowerBound],
            ),
          },
  };

  data.parameters = formData.parameters.map((row) => ({
    [discountParametersFields.calculationType]:
      row[discountParametersFields.calculationType]?.value,
    [discountParametersFields.discountBasis]:
      row[discountParametersFields.discountBasis]?.value,
    [discountParametersFields.discountBasisValue]:
    getConvertedFormattedNumber(row[discountParametersFields.discountBasisValue]),
    [discountParametersFields.boundType]: row[discountParametersFields.boundType]?.value,
    [discountParametersFields.lowerBound]:
       getConvertedFormattedNumber(row[discountParametersFields.lowerBound]),
    [discountParametersFields.upperBound]:
       getConvertedFormattedNumber(row[discountParametersFields.upperBound]),
    [discountParametersFields.balancing]: row[discountParametersFields.balancing]?.value,
    [discountParametersFields.accessFeeRate]:
       getConvertedFormattedNumber(row[discountParametersFields.accessFeeRate]),
    [discountParametersFields.incrementalRate]:
       getConvertedFormattedNumber(row[discountParametersFields.incrementalRate]),
  }));

  if (formData[commitmentDistributionParameters]) {
    data[commitmentDistributionParameters] = formData[commitmentDistributionParameters]
      .map((row) => ({
        [commitmentDistributionParametersFields.homeOperators]:
          getIds(row[commitmentDistributionParametersFields.homeOperators]),
        [commitmentDistributionParametersFields.partnerOperators]:
          getIds(row[commitmentDistributionParametersFields.partnerOperators]),
        [commitmentDistributionParametersFields.charge]:
          getConvertedFormattedNumber(row[commitmentDistributionParametersFields.charge]),
      }));
  } else {
    data[commitmentDistributionParameters] = null;
  }

  return data;
};

export default getConvertedForRequestFormData;
