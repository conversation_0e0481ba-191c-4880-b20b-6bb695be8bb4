import getIds from 'core/utilities/getIds';
import getArrayByKey from 'core/utilities/getArrayByKey';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import {
  commitmentDistributionParameters,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/CommitmentDistributionParameters/constants';

import {
  discountQualifyingFields,
  discountQualifyingRule,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountQualifying/constants';

import {
  discountModelTypeField,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountModelType/constants';
import getConvertedForRequestFormData from './utilities';

jest.mock('core/utilities/getIds', () => jest.fn());
jest.mock('core/utilities/getArrayByKey', () => jest.fn());

describe('AgreementDetails: AgreementItems: Discounts: shared: DiscountForm: utilities: getConvertedForRequestFormData', () => {
  const mockFormData = {
    [discountModelTypeField]: { value: 'modelType' },
    [discountFields.homeOperators]: ['operator1', 'operator2'],
    [discountFields.partnerOperators]: ['partner1'],
    [discountFields.validTo]: '2025-12-31',
    [discountFields.validFrom]: '2025-01-01',
    [discountFields.discountDirection]: { value: 'inbound' },
    [discountFields.serviceTypes]: [{ value: 'voice' }],
    [discountFields.callDestinations]: [{ value: 'destination1' }],
    [discountFields.calledCountries]: ['US', 'UK'],
    [discountFields.imsiCountType]: { value: 'countType' },
    [discountFields.trafficSegments]: [{ id: 'segment1' }],
    [discountFields.discountSettlementMethod]: { value: 'method1' },
    [discountFields.discountCurrency]: { code: 'USD' },
    [discountFields.rateAboveCommitment]: '123.45',
    [discountFields.inboundMarketShare]: '67.89',
    [discountFields.financialThreshold]: '100.50',
    [discountFields.aboveThresholdRate]: '0.1234567890',
    [discountFields.taxType]: 'VAT',
    [discountFields.volumeType]: 'volume1',
    [discountQualifyingRule]: {
      [discountQualifyingFields.direction]: { value: 'outbound' },
      [discountQualifyingFields.serviceTypes]: [{ value: 'data' }],
      [discountQualifyingFields.basis]: { value: 'basis1' },
      [discountQualifyingFields.upperBound]: '500',
      [discountQualifyingFields.lowerBound]: '100',
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: { value: 'type1' },
        [discountParametersFields.discountBasis]: { value: 'basis2' },
        [discountParametersFields.discountBasisValue]: '200',
        [discountParametersFields.boundType]: { value: 'bound1' },
        [discountParametersFields.lowerBound]: '50',
        [discountParametersFields.upperBound]: '300',
        [discountParametersFields.balancing]: { value: 'balance1' },
        [discountParametersFields.accessFeeRate]: '15.5',
        [discountParametersFields.incrementalRate]: '25.75',
      },
    ],
  };

  beforeEach(() => {
    getIds.mockImplementation((data) => data.map((item) => item));
    getArrayByKey.mockImplementation((data, key) => data.map((item) => item[key]));
  });

  test('should correctly convert form data for request', () => {
    const result = getConvertedForRequestFormData(mockFormData);

    expect(result).toEqual({
      [discountModelTypeField]: 'modelType',
      [discountFields.homeOperators]: ['operator1', 'operator2'],
      [discountFields.partnerOperators]: ['partner1'],
      [discountFields.validTo]: '2025-12-31',
      [discountFields.validFrom]: '2025-01-01',
      [discountFields.discountDirection]: 'inbound',
      [discountFields.serviceTypes]: ['voice'],
      [discountFields.callDestinations]: ['destination1'],
      [discountFields.calledCountries]: ['US', 'UK'],
      [discountFields.imsiCountType]: 'countType',
      [discountFields.trafficSegments]: ['segment1'],
      [discountFields.discountSettlementMethod]: 'method1',
      [discountFields.discountCurrency]: 'USD',
      [discountFields.rateAboveCommitment]: 123.45,
      [discountFields.inboundMarketShare]: 67.89,
      [discountFields.financialThreshold]: 100.50,
      [discountFields.aboveThresholdRate]: 0.1234567890,
      [discountFields.taxType]: 'VAT',
      [discountFields.volumeType]: 'volume1',
      [discountQualifyingRule]: {
        [discountQualifyingFields.direction]: 'outbound',
        [discountQualifyingFields.serviceTypes]: ['data'],
        [discountQualifyingFields.basis]: 'basis1',
        [discountQualifyingFields.upperBound]: 500,
        [discountQualifyingFields.lowerBound]: 100,
      },
      parameters: [
        {
          [discountParametersFields.calculationType]: 'type1',
          [discountParametersFields.discountBasis]: 'basis2',
          [discountParametersFields.discountBasisValue]: 200,
          [discountParametersFields.boundType]: 'bound1',
          [discountParametersFields.lowerBound]: 50,
          [discountParametersFields.upperBound]: 300,
          [discountParametersFields.balancing]: 'balance1',
          [discountParametersFields.accessFeeRate]: 15.5,
          [discountParametersFields.incrementalRate]: 25.75,
        },
      ],
      [commitmentDistributionParameters]: null,
    });
  });

  test('should handle empty or null values correctly', () => {
    const formDataWithEmptyValues = {
      ...mockFormData,
      [discountFields.rateAboveCommitment]: '',
      [discountFields.inboundMarketShare]: null,
      [discountFields.financialThreshold]: '',
      [discountFields.aboveThresholdRate]: null,
    };

    const result = getConvertedForRequestFormData(formDataWithEmptyValues);

    expect(result[discountFields.rateAboveCommitment]).toBe('');
    expect(result[discountFields.inboundMarketShare]).toBe(null);
    expect(result[discountFields.financialThreshold]).toBe('');
    expect(result[discountFields.aboveThresholdRate]).toBe(null);
  });

  test('should handle empty discount rule correctly', () => {
    const formDataWithEmptyValues = {
      ...mockFormData,
      [discountQualifyingRule]: {
        [discountQualifyingFields.direction]: '',
        [discountQualifyingFields.serviceTypes]: [],
        [discountQualifyingFields.basis]: '',
        [discountQualifyingFields.upperBound]: null,
        [discountQualifyingFields.lowerBound]: undefined,
      },
    };

    const result = getConvertedForRequestFormData(formDataWithEmptyValues);

    expect(result[discountQualifyingRule]).toBe(null);
  });
});
