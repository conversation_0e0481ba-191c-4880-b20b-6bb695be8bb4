import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { cloneDeep } from 'lodash';

import {
  taxTypeConfig, volumeTypeConfig,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConfigs';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  defaultDiscountQualifyingValues,
  discountQualifyingRule,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountQualifying/constants';
import {
  discountModelTypeField,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountModelType/constants';
import {
  commitmentDistributionParameters,
  defaultCommitmentDistributionParameterValues,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/CommitmentDistributionParameters/constants';

const emptyParameterValues = {
  [discountParametersFields.discountBasisValue]: '',
  [discountParametersFields.lowerBound]: '',
  [discountParametersFields.upperBound]: '',
  [discountParametersFields.incrementalRate]: null,
};

const useDefaultDiscountFormValuesByModelType = ({
  availableValuesFromConfigByModelType,
  availableValuesFromDefaultConfigByModelType,
  initialFormikValues,
  modelType,
  triggerDeleteParameterBtn,
  triggerAddParameterBtn,
  triggerAddCommitmentDistributionParameterBtn,
  triggerDeleteCommitmentDistributionParameterBtn,
  commitmentSplit,
}) => {
  const agreementParametersData = useSelector((state) => state.agreementParameters.data);
  const clientCurrency = useSelector((state) => state.clientCurrency.data);

  const homeOperators = agreementParametersData?.home_operators || [];
  const partnerOperators = agreementParametersData?.partner_operators || [];
  const startDate = agreementParametersData?.start_date || '';
  const endDate = agreementParametersData?.end_date || '';

  const getDefaultValues = () => {
    const defaultTrafficValues = {
      [discountFields.homeOperators]: homeOperators,
      [discountFields.partnerOperators]: partnerOperators,
      [discountFields.validFrom]: startDate,
      [discountFields.validTo]: endDate,
      [discountFields.discountDirection]: null,
      [discountFields.serviceTypes]: [],
      [discountFields.callDestinations]: [],
      [discountFields.calledCountries]: [],
      [discountFields.trafficSegments]: [],
      [discountFields.discountSettlementMethod]:
          availableValuesFromDefaultConfigByModelType[discountFields.discountSettlementMethod][0],
      [discountFields.imsiCountType]:
          availableValuesFromDefaultConfigByModelType[discountFields.imsiCountType]?.[0] || null,
      [discountFields.discountCurrency]: clientCurrency,
      [discountFields.rateAboveCommitment]: null,
      [discountFields.inboundMarketShare]: null,
      [discountFields.financialThreshold]: null,
      [discountFields.aboveThresholdRate]: null,
      [discountFields.taxType]: taxTypeConfig[0].value,
      [discountFields.volumeType]: volumeTypeConfig[0].value,
      [discountQualifyingRule]: defaultDiscountQualifyingValues,
    };

    const defaultParametersValues = availableValuesFromDefaultConfigByModelType.parameters
      .map((parameter) => ({
        [discountParametersFields.calculationType]:
          parameter[discountParametersFields.calculationType][0],
        [discountParametersFields.discountBasis]:
          parameter[discountParametersFields.discountBasis][0],
        [discountParametersFields.balancing]:
          parameter[discountParametersFields.balancing]?.[0] || null,
        [discountParametersFields.accessFeeRate]:
        parameter[discountParametersFields.accessFeeRate]?.[0] || null,
        [discountParametersFields.boundType]:
          parameter[discountParametersFields.boundType][0],
        ...emptyParameterValues,
      }));

    return {
      ...defaultTrafficValues,
      parameters: defaultParametersValues,
      [commitmentDistributionParameters]: null,
    };
  };

  const [
    defaultDiscountFormValuesByModelType,
    setDefaultDiscountFormValuesByModelType] = useState(initialFormikValues || getDefaultValues());

  const getValuesBasedOnInitialFormikValues = () => {
    const lastParameterIndex = defaultDiscountFormValuesByModelType
      .parameters.length - 1;
    const lastParameter = defaultDiscountFormValuesByModelType
      .parameters[lastParameterIndex];
    const diffParametersAmount = defaultDiscountFormValuesByModelType.parameters.length
          - availableValuesFromConfigByModelType.parameters.length;

    if (!diffParametersAmount) {
      return defaultDiscountFormValuesByModelType;
    }

    const newParameters = Array(diffParametersAmount).fill(undefined).map(() => lastParameter);
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);

    valuesBasedOnInitialValues.availableValues.parameters.push(...newParameters);

    return valuesBasedOnInitialValues;
  };

  const addDefaultCommitmentDistributionParameterValue = () => {
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);

    valuesBasedOnInitialValues[commitmentDistributionParameters].push({
      ...defaultCommitmentDistributionParameterValues,
    });

    setDefaultDiscountFormValuesByModelType(valuesBasedOnInitialValues);
  };

  const deleteDefaultCommitmentDistributionParameterValue = () => {
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);

    valuesBasedOnInitialValues[
      commitmentDistributionParameters
    ] = valuesBasedOnInitialValues[commitmentDistributionParameters]
      .filter((parameter, i) => (
        i !== triggerDeleteCommitmentDistributionParameterBtn.index
      ));

    setDefaultDiscountFormValuesByModelType(valuesBasedOnInitialValues);
  };

  const addDefaultParameterValue = () => {
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);
    const lastParameterIndex = defaultDiscountFormValuesByModelType
      .parameters.length - 1;
    const lastParameter = defaultDiscountFormValuesByModelType
      .parameters[lastParameterIndex];

    valuesBasedOnInitialValues.parameters.push({
      ...lastParameter,
      ...emptyParameterValues,
    });

    setDefaultDiscountFormValuesByModelType(valuesBasedOnInitialValues);
  };

  const deleteDefaultParameterValue = () => {
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);

    valuesBasedOnInitialValues.parameters.pop();

    setDefaultDiscountFormValuesByModelType(valuesBasedOnInitialValues);
  };

  const addCommitmentDistributionParameters = () => {
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);

    const newCommitmentDistributionParameters = availableValuesFromDefaultConfigByModelType[
      commitmentDistributionParameters
    ]
      .map(() => ({
        ...defaultCommitmentDistributionParameterValues,
      }));

    valuesBasedOnInitialValues[
      commitmentDistributionParameters
    ] = newCommitmentDistributionParameters;

    setDefaultDiscountFormValuesByModelType(valuesBasedOnInitialValues);
  };

  const deleteCommitmentDistributionParameters = () => {
    const valuesBasedOnInitialValues = cloneDeep(defaultDiscountFormValuesByModelType);

    valuesBasedOnInitialValues[commitmentDistributionParameters] = null;

    setDefaultDiscountFormValuesByModelType(valuesBasedOnInitialValues);
  };

  useEffect(() => {
    if (commitmentSplit) {
      addCommitmentDistributionParameters();
    } else {
      deleteCommitmentDistributionParameters();
    }
  }, [commitmentSplit]);

  useEffect(() => {
    if (triggerAddCommitmentDistributionParameterBtn) {
      addDefaultCommitmentDistributionParameterValue();
    }
  }, [triggerAddCommitmentDistributionParameterBtn]);

  useEffect(() => {
    if (triggerDeleteCommitmentDistributionParameterBtn.trigger) {
      deleteDefaultCommitmentDistributionParameterValue();
    }
  }, [triggerDeleteCommitmentDistributionParameterBtn]);

  useEffect(() => {
    if (triggerAddParameterBtn) {
      addDefaultParameterValue();
    }
  }, [triggerAddParameterBtn]);

  useEffect(() => {
    if (triggerDeleteParameterBtn) {
      deleteDefaultParameterValue();
    }
  }, [triggerDeleteParameterBtn]);

  useEffect(() => {
    const newDefaultDiscountFormValues = initialFormikValues
        && modelType === initialFormikValues[discountModelTypeField]
      ? getValuesBasedOnInitialFormikValues()
      : getDefaultValues();

    setDefaultDiscountFormValuesByModelType(newDefaultDiscountFormValues);
  }, [modelType]);

  return {
    defaultDiscountFormValuesByModelType,
  };
};

export default useDefaultDiscountFormValuesByModelType;
