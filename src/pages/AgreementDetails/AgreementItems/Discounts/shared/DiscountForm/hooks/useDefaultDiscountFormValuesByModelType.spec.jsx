import React from 'react';
import { renderHook } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import {
  modelTypesOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import configForSingleRateEffective
  from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountModelType/DiscountFormConfigByModelType/configs/configForSingleRateEffective';
import useDefaultDiscountFormValuesByModelType from './useDefaultDiscountFormValuesByModelType';

const mockStore = configureStore([]);

describe('AgreementDetails: AgreementItems: Discounts: shared: DiscountForm: hooks: useDefaultDiscountFormValuesByModelType', () => {
  let store;
  const defaultState = {
    agreementParameters: {
      data: {
        home_operators: ['Operator1'], partner_operators: ['Operator2'], start_date: '2024-01-01', end_date: '2024-12-31',
      },
    },
    clientCurrency: { data: 'USD' },
  };

  const initialFormikValues = null;
  const modelType = modelTypesOptions.singleRateEffective.value;
  const availableValuesFromConfigByModelType = configForSingleRateEffective.availableValues;
  const availableValuesFromDefaultConfigByModelType = configForSingleRateEffective.availableValues;

  const renderUseDefaultDiscountFormValuesByModelType = (overrideState = {}) => {
    store = mockStore({ ...defaultState, ...overrideState });
    return renderHook(() => useDefaultDiscountFormValuesByModelType({
      availableValuesFromConfigByModelType,
      availableValuesFromDefaultConfigByModelType,
      initialFormikValues,
      modelType,
      triggerDeleteParameterBtn: 0,
      triggerAddParameterBtn: 0,
      triggerAddCommitmentDistributionParameterBtn: 0,
      triggerDeleteCommitmentDistributionParameterBtn: { trigger: 0, index: undefined },
    }), { wrapper: ({ children }) => <Provider store={store}>{children}</Provider> });
  };

  test('should return correct default discount form values when initialFormikValues is null', () => {
    const { result } = renderUseDefaultDiscountFormValuesByModelType();

    const expectedValues = {
      above_commitment_rate: null,
      call_destinations: [],
      called_countries: [],
      currency_code: 'USD',
      direction: null,
      end_date: '2024-12-31',
      home_operators: [
        'Operator1',
      ],
      inbound_market_share: null,
      financial_threshold: null,
      above_financial_threshold_rate: null,
      imsi_count_type: null,
      parameters: [
        {
          access_fee_rate: null,
          balancing: null,
          basis: {
            title: 'Value',
            value: 'VALUE',
          },
          basis_value: '',
          calculation_type: {
            title: 'Single Rate Effective',
            value: 'SINGLE_RATE_EFFECTIVE',
          },
          incremental_rate: null,
          lower_bound: '',
          upper_bound: '',
          bound_type: undefined,
        },
      ],
      partner_operators: [
        'Operator2',
      ],
      qualifying_rule: {
        basis: null,
        direction: null,
        lower_bound: null,
        service_types: [],
        upper_bound: null,
      },
      service_types: [],
      settlement_method: {
        title: 'Credit Note EoA',
        value: 'CREDIT_NOTE_EOA',
      },
      start_date: '2024-01-01',
      tax_type: 'NET',
      traffic_segments: [],
      volume_type: 'ACTUAL',
      commitment_distribution_parameters: null,
    };

    expect(result.current.defaultDiscountFormValuesByModelType).toEqual(expectedValues);
  });
});
