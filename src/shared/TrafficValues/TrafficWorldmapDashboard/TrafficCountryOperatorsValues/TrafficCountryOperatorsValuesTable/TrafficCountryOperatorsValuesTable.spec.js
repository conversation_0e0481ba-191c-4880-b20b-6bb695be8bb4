import React from 'react';
import { act, render, screen } from '@testing-library/react';
import { configureStore } from '@reduxjs/toolkit';
import { Provider } from 'react-redux';
import { AppContextProvider } from 'AppContextProvider';
import mockBudgetCountryOperatorsValuesData, {
  moreCasesMockBudgetCountryOperatorsValuesData,
} from 'features/GetBudgetCountryOperatorsValues/mockBudgetCountryOperatorsValuesData';
import mockOperatorsList from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/mockOperatorsList';

import rootReducer from 'core/rootReducer';
import TrafficCountryOperatorsValuesTable from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/TrafficCountryOperatorsValuesTable';

describe('TrafficDetails: TrafficValues: TrafficWorldmapDashboard: TrafficCountryOperatorsValues: TrafficCountryOperatorsValuesTable', () => {
  const store = configureStore({ reducer: rootReducer });
  const trafficCountryOperatorsValuesTable = (props) => (
    <Provider store={store}>
      <AppContextProvider>
        <TrafficCountryOperatorsValuesTable {...props} />
      </AppContextProvider>
    </Provider>
  );
  const unitType = 'volume';
  const defaultProps = {
    data: mockBudgetCountryOperatorsValuesData.data,
    unitType,
  };

  test('should be mock data in the DOM', async () => {
    await act(() => render(trafficCountryOperatorsValuesTable(defaultProps)));

    const volume = screen.getByText(mockBudgetCountryOperatorsValuesData.data[0].records[0].value);

    expect(volume).toBeInTheDocument();
  });

  test('should display correct column headers', async () => {
    await act(() => render(trafficCountryOperatorsValuesTable(defaultProps)));

    expect(screen.getByText('PMN code')).toBeInTheDocument();
    expect(screen.getByText('Operator name')).toBeInTheDocument();
    expect(screen.getByText('Country share, %')).toBeInTheDocument();
  });

  test('should display operator names when operatorsList is provided', async () => {
    const propsWithOperators = {
      ...defaultProps,
      operatorsList: mockOperatorsList,
    };

    await act(() => render(trafficCountryOperatorsValuesTable(propsWithOperators)));

    // Check that operator names from mockOperatorsList are displayed
    expect(screen.getByText('lifecell LLC')).toBeInTheDocument(); // name from mockOperatorsList[0]
    expect(screen.getByText('Kyivstar JSC')).toBeInTheDocument(); // name from mockOperatorsList[1]
  });

  test('should be "Table Failure" in the DOM', async () => {
    const errorMessage = 'Request failed';
    const testProps = {
      ...defaultProps,
      trafficCountryOperatorsValuesError: {
        response: {
          data: {
            detail: errorMessage,
          },
        },
      },

    };
    await act(() => render(trafficCountryOperatorsValuesTable(testProps)));

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  test('should be "No data" in the DOM', async () => {
    const testProps = {
      ...defaultProps,
      data: [],
    };

    await act(() => render(trafficCountryOperatorsValuesTable(testProps)));

    expect(screen.getByText('No data')).toBeInTheDocument();
  });

  test('should be Preloader in the DOM', async () => {
    const testProps = {
      ...defaultProps,
      trafficCountryOperatorsValuesIsLoading: true,
    };
    await act(() => render(trafficCountryOperatorsValuesTable(testProps)));

    expect(screen.getByTestId('traffic-country-operators-values__preloader')).toBeInTheDocument();
  });

  describe('Comparison Mode', () => {
    const trafficsComparisonData = [{
      value: {
        id: 12,
        name: 'Test1',
      },
    },
    {
      value: {
        id: 14,
        name: 'Test2',
      },
    }];

    const testPropsForComparison = {
      data: moreCasesMockBudgetCountryOperatorsValuesData.data,
      isComparisonMode: true,
      trafficComparisonData: trafficsComparisonData,
      unitType,
    };

    test('should be mock data in the DOM', async () => {
      await act(() => render(trafficCountryOperatorsValuesTable(testPropsForComparison)));

      const volume0 = screen.getByText(testPropsForComparison.data[0].records[0].value);
      const volume1 = screen.getByText(testPropsForComparison.data[1].records[0].value);

      expect(volume0).toBeInTheDocument();
      expect(volume1).toBeInTheDocument();
    });

    test('should be markers in the DOM', async () => {
      await act(() => render(trafficCountryOperatorsValuesTable(testPropsForComparison)));
      const markersAmount = screen.getAllByTestId('marker').length;

      expect(markersAmount)
      // eslint-disable-next-line max-len
        .toBe((testPropsForComparison.data[0].records.length + 1) * trafficsComparisonData.length);
    });
  });
});
