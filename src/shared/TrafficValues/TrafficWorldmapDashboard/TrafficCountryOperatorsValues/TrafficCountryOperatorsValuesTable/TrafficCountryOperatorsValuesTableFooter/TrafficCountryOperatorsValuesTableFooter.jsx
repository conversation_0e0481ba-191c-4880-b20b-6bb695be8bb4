import React from 'react';
import PropTypes from 'prop-types';

import {
  TableRow,
  TableCell, TableFooter,
} from '@mui/material';

import FormattedTableCell from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/TrafficCountryOperatorsValuesTable/FormattedTableCell';
import { modifiers } from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/TrafficCountryOperatorsValuesTable/constants';

import Marker from 'shared/Marker';
import FormattedNumber from 'shared/FormattedNumber';

const TrafficCountryOperatorsValuesTableFooter = ({
  data,
  canHavePreviousYear,
  isComparisonMode,
  trafficComparisonData,
}) => {
  const getTableCellValues = (key) => (data.map(
    (traffic) => (
      <div className={modifiers.tableCellItem} key={traffic.budget_id}>
        <FormattedNumber
          value={traffic[key]}
          isNeedRound
        />
      </div>
    ),
  ));

  return (
    <TableFooter className={modifiers.tableFooter}>
      <TableRow className={modifiers.tableRow}>
        <TableCell className={modifiers.tableCell}>
          <div className={modifiers.tableCellItemOperatorWrap}>
            <div className={modifiers.tableCellItemOperator}>Total</div>
            {isComparisonMode && (
            <div>
              {trafficComparisonData.map(
                (traffic, index) => {
                  const { markerColor } = traffic;

                  return (
                    <div className={modifiers.tableCellItem} key={traffic.budget_id || index}>
                      <Marker backgroundColor={markerColor} />
                    </div>
                  );
                },
              )}
            </div>
            )}
          </div>
        </TableCell>
        <TableCell className={modifiers.tableCell}>
          {/* Empty cell for Operator name column */}
        </TableCell>
        <TableCell className={modifiers.tableCell}>
          {getTableCellValues('total_value')}
        </TableCell>
        {canHavePreviousYear && (
        <>
          <TableCell className={modifiers.tableCell}>
            {getTableCellValues('total_previous_year_value')}
          </TableCell>
          <FormattedTableCell className={modifiers.tableCell} value="" />
        </>
        )}
        <TableCell className={modifiers.tableCell}>
          {getTableCellValues('total_country_share')}
        </TableCell>
      </TableRow>
    </TableFooter>
  );
};

TrafficCountryOperatorsValuesTableFooter.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    records: PropTypes.arrayOf(PropTypes.shape({
      country_share: PropTypes.string,
      dynamic: PropTypes.string,
      operator_id: PropTypes.number,
      previous_year_value: PropTypes.string,
      value: PropTypes.string,
    })),
  })),
  canHavePreviousYear: PropTypes.bool,
  isComparisonMode: PropTypes.bool,
  trafficComparisonData: PropTypes.instanceOf(Array),
};

TrafficCountryOperatorsValuesTableFooter.defaultProps = {
  data: [{
    country_share: 0,
    dynamic: 0,
    operator_id: 0,
    previous_year_value: 0,
    value: 0,
  }],
  canHavePreviousYear: false,
  isComparisonMode: false,
  trafficComparisonData: [],
};

export default TrafficCountryOperatorsValuesTableFooter;
