import React from 'react';
import PropTypes from 'prop-types';

import {
  TableRow,
  TableHead, TableCell,
} from '@mui/material';

import { modifiers } from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/TrafficCountryOperatorsValuesTable/constants';
import {
  unitTypeConfig,
} from 'shared/TrafficValues/shared/UnitType/constants';

const TrafficCountryOperatorsValuesTableHead = ({
  canHavePreviousYear, unitType, serviceTypeUnit,
}) => {
  const unitTypeLabel = unitTypeConfig.volume.value === unitType
    ? `${unitTypeConfig.volume.label}, ${serviceTypeUnit}`
    : unitTypeConfig.charge.label;

  return (
    <TableHead className={modifiers.tableHead} data-testid={modifiers.tableHead}>
      <TableRow className={modifiers.tableRow}>
        <TableCell className={modifiers.tableCell}>
          PMN code
        </TableCell>
        <TableCell className={modifiers.tableCell}>
          Operator name
        </TableCell>
        <TableCell className={modifiers.tableCell}>
          {unitTypeLabel}
        </TableCell>
        { canHavePreviousYear && (
        <>
          <TableCell className={modifiers.tableCell}>
            {unitTypeLabel}
            {' '}
            (Previous year)
          </TableCell>
          <TableCell className={modifiers.tableCell}>
            Dynamic, %
          </TableCell>
        </>
        )}
        <TableCell className={modifiers.tableCell}>
          Country share, %
        </TableCell>
      </TableRow>
    </TableHead>
  );
};

TrafficCountryOperatorsValuesTableHead.propTypes = {
  canHavePreviousYear: PropTypes.bool,
  unitType: PropTypes.string.isRequired,
  serviceTypeUnit: PropTypes.string,
};

TrafficCountryOperatorsValuesTableHead.defaultProps = {
  canHavePreviousYear: false,
  serviceTypeUnit: 'MB',
};

export default TrafficCountryOperatorsValuesTableHead;
